import https from "https";
import crypto from "crypto";

/**
 * Creates a hardened HTTPS agent for R2 connections with secure TLS configuration
 */
export function createR2Agent(): https.Agent {
  return new https.Agent({
    minVersion: "TLSv1.2",
    secureOptions: crypto.constants.SSL_OP_NO_SSLv2 | crypto.constants.SSL_OP_NO_SSLv3,
    ciphers: "ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM",
    keepAlive: true,
    timeout: 60000,
    maxSockets: 50,
  });
}
