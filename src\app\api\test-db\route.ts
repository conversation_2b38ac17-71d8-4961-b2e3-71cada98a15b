import { NextRequest, NextResponse } from "next/server";
import { dbconnect } from "@/lib/db";
import { testMongoConnection } from "@/lib/db-fallback";
import mongoose from "mongoose";

// GET /api/test-db - Test MongoDB connection
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    console.log("🧪 Starting MongoDB connection test...");
    
    // Test 1: Check environment variables
    const envCheck = {
      mongodbUri: !!process.env.MONGODB_URI,
      mongodbUriPattern: process.env.MONGODB_URI?.replace(/:\/\/[^@]+@/, "://***:***@"),
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    };

    console.log("📋 Environment check:", envCheck);

    // Test 2: Try main connection
    let mainConnectionResult = null;
    try {
      console.log("🔄 Testing main connection...");
      const connection = await dbconnect();
      
      // Test a simple operation
      if (!connection.db) {
        throw new Error('Database connection not established');
      }
      const admin = connection.db.admin();
      const pingResult = await admin.ping();
      
      mainConnectionResult = {
        success: true,
        readyState: connection.readyState,
        host: connection.host,
        port: connection.port,
        name: connection.name,
        ping: pingResult
      };
      
      console.log("✅ Main connection successful");
    } catch (error: any) {
      console.error("❌ Main connection failed:", error.message);
      mainConnectionResult = {
        success: false,
        error: error.message,
        stack: error.stack?.split('\n').slice(0, 5) // First 5 lines of stack
      };
    }

    // Test 3: Try fallback connection
    let fallbackConnectionResult = null;
    try {
      console.log("🔄 Testing fallback connection...");
      fallbackConnectionResult = await testMongoConnection();
      console.log("✅ Fallback connection test completed");
    } catch (error: any) {
      console.error("❌ Fallback connection failed:", error.message);
      fallbackConnectionResult = {
        success: false,
        error: error.message
      };
    }

    // Test 4: Network connectivity test
    const networkTest = await testNetworkConnectivity();

    const totalTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      testResults: {
        environment: envCheck,
        mainConnection: mainConnectionResult,
        fallbackConnection: fallbackConnectionResult,
        networkTest,
        totalTestTime: `${totalTime}ms`,
        recommendations: generateRecommendations(mainConnectionResult, fallbackConnectionResult, networkTest)
      }
    });

  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error("❌ Database test failed:", error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      testTime: `${totalTime}ms`,
      troubleshooting: [
        "Check your internet connection",
        "Verify MongoDB Atlas cluster is running",
        "Check Network Access settings in MongoDB Atlas",
        "Try connecting from a different network",
        "Contact your network administrator if behind corporate firewall"
      ]
    }, { status: 500 });
  }
}

// Test network connectivity to MongoDB Atlas
async function testNetworkConnectivity() {
  try {
    console.log("🌐 Testing network connectivity...");
    
    // Test DNS resolution
    const dnsTest = await fetch('https://dns.google/resolve?name=cluster0.rgfqa.mongodb.net&type=A')
      .then(res => res.json())
      .catch(err => ({ error: err.message }));

    // Test general internet connectivity
    const internetTest = await fetch('https://www.google.com', { 
      method: 'HEAD',
      signal: AbortSignal.timeout(5000)
    })
      .then(() => ({ success: true }))
      .catch(err => ({ success: false, error: err.message }));

    return {
      dns: dnsTest,
      internet: internetTest,
      timestamp: new Date().toISOString()
    };
  } catch (error: any) {
    return {
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// Generate recommendations based on test results
function generateRecommendations(mainResult: any, fallbackResult: any, networkTest: any) {
  const recommendations = [];

  if (!mainResult?.success && !fallbackResult?.success) {
    recommendations.push("🚨 Both main and fallback connections failed");
    
    if (networkTest?.internet?.success === false) {
      recommendations.push("🌐 Internet connectivity issue detected - check your network");
    }
    
    if (networkTest?.dns?.error) {
      recommendations.push("🔍 DNS resolution issue - try using different DNS servers (8.8.8.8, 1.1.1.1)");
    }
    
    recommendations.push("🔧 Try connecting from a different network or disable VPN");
    recommendations.push("🏢 If behind corporate firewall, contact IT to whitelist MongoDB Atlas");
  } else if (!mainResult?.success && fallbackResult?.success) {
    recommendations.push("✅ Fallback connection works - consider using fallback configuration");
  } else if (mainResult?.success) {
    recommendations.push("✅ Main connection is working properly");
  }

  return recommendations;
}
