import { NextRequest, NextResponse } from "next/server";
import { createR2Client } from "@/lib/r2-client";

// Function to validate object key for security
function isValidObjectKey(key: string): boolean {
  // Check for null/undefined/empty
  if (!key || typeof key !== "string") {
    return false;
  }

  // Check for path traversal attempts
  if (
    key.includes("../") ||
    key.includes("..\\") ||
    key.startsWith("/") ||
    key.startsWith("\\")
  ) {
    return false;
  }

  // Check for potentially dangerous characters
  const dangerousChars = /[<>:"|?*\x00-\x1f\x7f]/;
  if (dangerousChars.test(key)) {
    return false;
  }

  // Check length (reasonable limit)
  if (key.length > 1024) {
    return false;
  }

  // Must contain only safe characters (letters, numbers, hyphens, underscores, dots, forward slashes for folders)
  const safePattern = /^[a-zA-Z0-9._\-\/]+$/;
  return safePattern.test(key);
}

export async function GET(request: NextRequest) {
  // Get the object key from the query parameters
  const searchParams = request.nextUrl.searchParams;
  const objectKey = searchParams.get("key");

  if (!objectKey) {
    return NextResponse.json(
      {
        success: false,
        error: "Missing 'key' parameter",
      },
      { status: 400 }
    );
  }

  // Validate the object key for security
  if (!isValidObjectKey(objectKey)) {
    console.warn(`Invalid object key attempted: ${objectKey}`);
    return NextResponse.json(
      {
        success: false,
        error: "Invalid object key format",
      },
      { status: 400 }
    );
  }

  try {
    console.log(`Checking R2 object: ${objectKey}`);

    // Attempt to fetch metadata directly using the public URL
    const publicUrl = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL}/${objectKey}`;

    let objectExists = false;
    let isPubliclyAccessible = false;
    let fetchError = null;
    let contentType = null;
    let contentLength = null;
    let lastModified = null;

    try {
      const response = await fetch(publicUrl);
      isPubliclyAccessible = response.ok;

      if (response.ok) {
        objectExists = true;
        contentType = response.headers.get("Content-Type");
        contentLength = response.headers.get("Content-Length");
        lastModified = response.headers.get("Last-Modified");
      } else {
        fetchError = {
          status: response.status,
          statusText: response.statusText,
        };
      }
    } catch (fetchError) {
      fetchError = fetchError instanceof Error ? fetchError.message : String(fetchError);
    }

    return NextResponse.json({
      success: objectExists,
      objectExists,
      publicUrl,
      isPubliclyAccessible,
      fetchError,
      objectMetadata: {
        contentType,
        contentLength,
        lastModified,
      },
    });
  } catch (error) {
    console.error("Error checking R2 object:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}
