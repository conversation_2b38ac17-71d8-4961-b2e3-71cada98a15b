"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

interface PendingSubscription {
  subscriptionId: string;
  communitySlug?: string;
  communityId?: string;
  timestamp: number;
}

export default function PaymentProcessingPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [status, setStatus] = useState<'checking' | 'success' | 'timeout' | 'error'>('checking');
  const [message, setMessage] = useState('Processing your payment...');
  const [timeElapsed, setTimeElapsed] = useState(0);

  useEffect(() => {
    // Check if there's a pending subscription
    const pendingSubscriptionData = localStorage.getItem('pendingSubscription');
    if (!pendingSubscriptionData) {
      // Check if user came from Razorpay by checking URL params or referrer
      const urlParams = new URLSearchParams(window.location.search);
      const fromRazorpay = urlParams.get('from') === 'razorpay' ||
                          document.referrer.includes('razorpay.com');

      if (fromRazorpay) {
        setStatus('error');
        setMessage('Payment session not found. Your payment may have been processed. Please check your subscription status.');
      } else {
        setStatus('error');
        setMessage('No pending payment found. Please try again.');
      }
      return;
    }

    const pendingSubscription: PendingSubscription = JSON.parse(pendingSubscriptionData);
    
    // Check if the subscription is too old (more than 30 minutes)
    const maxAge = 30 * 60 * 1000; // 30 minutes
    if (Date.now() - pendingSubscription.timestamp > maxAge) {
      localStorage.removeItem('pendingSubscription');
      setStatus('timeout');
      setMessage('Payment session expired. Please try again.');
      return;
    }

    let pollCount = 0;
    const maxPolls = 60; // Poll for up to 5 minutes (60 * 5 seconds)
    
    const pollSubscriptionStatus = async () => {
      try {
        const response = await fetch(`/api/community-subscriptions/status?subscriptionId=${pendingSubscription.subscriptionId}`);
        const data = await response.json();

        if (response.ok && data.subscription?.status === 'active') {
          // Payment successful!
          localStorage.removeItem('pendingSubscription');
          setStatus('success');
          setMessage('Payment successful! Redirecting to your community...');
          
          // Redirect to community page
          setTimeout(() => {
            if (pendingSubscription.communitySlug) {
              router.push(`/Newcompage/${pendingSubscription.communitySlug}?subscription=success`);
            } else if (pendingSubscription.communityId) {
              router.push(`/Newcompage/${pendingSubscription.communityId}?subscription=success`);
            } else {
              router.push('/admin/communities?subscription=success');
            }
          }, 2000);
          return;
        }

        pollCount++;
        if (pollCount >= maxPolls) {
          setStatus('timeout');
          setMessage('Payment verification is taking longer than expected. Please check your subscription status in your account.');
          return;
        }

        // Continue polling
        setTimeout(pollSubscriptionStatus, 5000); // Poll every 5 seconds
      } catch (error) {
        console.error('Error polling subscription status:', error);
        pollCount++;
        if (pollCount >= maxPolls) {
          setStatus('error');
          setMessage('Unable to verify payment status. Please check your subscription in your account.');
        } else {
          // Retry after a delay
          setTimeout(pollSubscriptionStatus, 5000);
        }
      }
    };

    // Start polling
    pollSubscriptionStatus();

    // Update time elapsed counter
    const timeInterval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);

    return () => {
      clearInterval(timeInterval);
    };
  }, [router]);

  const handleRetry = () => {
    localStorage.removeItem('pendingSubscription');
    router.push('/admin/communities');
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {status === 'checking' && (
          <>
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Processing Payment</h2>
            <p className="text-gray-600 mb-4">{message}</p>
            <p className="text-sm text-gray-500">Time elapsed: {formatTime(timeElapsed)}</p>
            <div className="mt-6 bg-blue-50 rounded-lg p-4">
              <p className="text-sm text-blue-800">
                We're verifying your payment with Razorpay. This usually takes a few seconds.
              </p>
            </div>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="text-green-600 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-green-900 mb-2">Payment Successful!</h2>
            <p className="text-green-700">{message}</p>
          </>
        )}

        {(status === 'timeout' || status === 'error') && (
          <>
            <div className="text-red-600 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-red-900 mb-2">
              {status === 'timeout' ? 'Verification Timeout' : 'Verification Error'}
            </h2>
            <p className="text-red-700 mb-6">{message}</p>
            <button
              type="button"
              onClick={handleRetry}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go to Dashboard
            </button>
          </>
        )}
      </div>
    </div>
  );
}
