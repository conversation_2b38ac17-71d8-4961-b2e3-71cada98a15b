import { NextRequest, NextResponse } from "next/server";
import crypto from "crypto";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const razorpayPaymentId = searchParams.get("razorpay_payment_id");
    const razorpaySubscriptionId = searchParams.get("razorpay_subscription_id");
    const razorpaySignature = searchParams.get("razorpay_signature");
    
    console.log("🔄 Payment callback received:", {
      hasPaymentId: !!razorpayPaymentId,
      hasSubscriptionId: !!razorpaySubscriptionId,
      hasSignature: !!razorpaySignature,
      allParams: Object.fromEntries(searchParams.entries())
    });

    // If we have payment details, verify and redirect
    if (razorpayPaymentId && razorpaySubscriptionId && razorpaySignature) {
      try {
        // Verify signature
        const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
        if (webhookSecret) {
          const body = `${razorpaySubscriptionId}|${razorpayPaymentId}`;
          const expectedSignature = crypto
            .createHmac("sha256", webhookSecret)
            .update(body)
            .digest("hex");

          if (expectedSignature !== razorpaySignature) {
            console.error("❌ Invalid signature in callback");
            return NextResponse.redirect(new URL("/billing?error=invalid_signature", request.url));
          }
        }

        console.log("✅ Payment callback verified successfully");
        
        // Redirect to success page - you can customize this URL
        const successUrl = new URL("/billing?subscription=success", request.url);
        successUrl.searchParams.set("payment_id", razorpayPaymentId);
        successUrl.searchParams.set("subscription_id", razorpaySubscriptionId);
        
        return NextResponse.redirect(successUrl);
        
      } catch (error) {
        console.error("❌ Error processing payment callback:", error);
        return NextResponse.redirect(new URL("/billing?error=processing_failed", request.url));
      }
    }

    // If payment was cancelled or failed
    const error = searchParams.get("error");
    if (error) {
      console.log("⚠️ Payment cancelled or failed:", error);
      return NextResponse.redirect(new URL(`/billing?error=${error}`, request.url));
    }

    // Default redirect if no specific parameters
    console.log("🔄 Default callback redirect");
    return NextResponse.redirect(new URL("/billing", request.url));

  } catch (error) {
    console.error("❌ Payment callback error:", error);
    return NextResponse.redirect(new URL("/billing?error=callback_failed", request.url));
  }
}

export async function POST(request: NextRequest) {
  // Handle POST callbacks if Razorpay sends them
  try {
    const body = await request.json();
    console.log("🔄 POST callback received:", body);
    
    // Process the callback data and redirect appropriately
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("❌ POST callback error:", error);
    return NextResponse.json({ error: "Callback processing failed" }, { status: 500 });
  }
}
