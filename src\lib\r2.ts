import { v4 as uuidv4 } from "uuid";
import { createR2Client, type RetryResult, type RetryOptions } from "./r2-client";

// Initialize R2 client
const r2Client = createR2Client();

/**
 * Generate a presigned URL for uploading a file to R2
 * @param fileName Original file name
 * @param fileType MIME type of the file
 * @param folder Folder path in R2 bucket
 * @returns Object with upload URL and the final R2 key
 */
export async function generateUploadUrl(
  fileName: string,
  fileType: string,
  folder: string = "uploads"
): Promise<{ uploadUrl: string; key: string }> {
  try {
    console.log(
      `Generating R2 upload URL for ${fileName} (${fileType}) in folder ${folder}`
    );

    // Generate a unique file path
    const key = r2Client.generateFilePath(fileName, folder);
    console.log(`Generated R2 key: ${key}`);

    // Generate a presigned URL for uploading
    const uploadUrl = r2Client.generatePresignedUploadUrl(key, fileType, 3600);

    console.log(`Generated presigned URL: ${uploadUrl.substring(0, 100)}...`);

    return {
      uploadUrl,
      key,
    };
  } catch (error) {
    console.error("Error generating R2 upload URL:", error);
    throw error;
  }
}

/**
 * Generate a presigned URL for downloading/viewing a file from R2
 * @param key R2 object key
 * @returns Presigned URL for downloading/viewing
 */
export async function generateDownloadUrl(key: string): Promise<string> {
  // For downloads, we can just return the public URL since R2 objects are public
  return r2Client.getPublicUrl(key);
}

/**
 * Delete a file from R2 with retry mechanism
 * @param key R2 object key
 * @param options Retry options
 * @returns Result with retry information
 */
export async function deleteFileWithRetry(key: string, options?: RetryOptions): Promise<RetryResult<boolean>> {
  return await r2Client.deleteFile(key, options);
}

/**
 * Delete a file from R2 (legacy function for backwards compatibility)
 * @param key R2 object key
 * @returns Success status
 */
export async function deleteFile(key: string): Promise<boolean> {
  try {
    const result = await deleteFileWithRetry(key);
    console.log(`File deleted successfully on attempt ${result.retry}`);
    return result.data;
  } catch (error) {
    console.error("Error deleting file from R2:", error);
    return false;
  }
}

/**
 * Get the public URL for a file in R2
 * @param key R2 object key
 * @returns Public URL
 */
export function getPublicUrl(key: string): string {
  console.log("[R2] getPublicUrl called with key:", key);

  // If using Cloudflare R2 public URL
  if (process.env.NEXT_PUBLIC_R2_PUBLIC_URL) {
    const publicUrl = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL}/${key}`;
    console.log(
      "[R2] Using NEXT_PUBLIC_R2_PUBLIC_URL. Generated URL:",
      publicUrl
    );
    return publicUrl;
  }

  // If using custom domain
  if (process.env.NEXT_PUBLIC_CUSTOM_DOMAIN) {
    const publicUrl = `${process.env.NEXT_PUBLIC_CUSTOM_DOMAIN}/${key}`;
    console.log(
      "[R2] Using NEXT_PUBLIC_CUSTOM_DOMAIN. Generated URL:",
      publicUrl
    );
    return publicUrl;
  }

  // Hardcoded fallback for R2 public URL
  // The correct format for public R2 URLs is https://pub-{account-id}.r2.dev/{key}
  const fallbackUrl = `https://pub-895f71ea78c843b59c97073ccfe523c5.r2.dev/${key}`;
  console.log("[R2] Using fallback URL. Generated URL:", fallbackUrl);
  return fallbackUrl;
}

/**
 * Generate a path for course content
 * @param courseId Course ID
 * @param moduleId Optional module ID
 * @param lessonId Optional lesson ID
 * @returns Path string
 */
export function generateCoursePath(
  courseId: string,
  moduleId?: string,
  lessonId?: string
): string {
  let path = `courses/${courseId}`;
  if (moduleId) {
    path += `/modules/${moduleId}`;
    if (lessonId) {
      path += `/lessons/${lessonId}`;
    }
  }
  return path;
}
