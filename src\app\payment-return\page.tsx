"use client";

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";

interface SubscriptionResult {
  success: boolean;
  message: string;
  subscription?: any;
}

function PaymentReturnContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('Verifying your payment...');
  const [result, setResult] = useState<SubscriptionResult | null>(null);

  useEffect(() => {
    const handlePaymentReturn = async () => {
      // Get parameters from URL
      const subscriptionId = searchParams.get('subscription_id');
      const paymentId = searchParams.get('payment_id');
      const signature = searchParams.get('signature');
      const communityId = searchParams.get('community_id');
      const communitySlug = searchParams.get('community_slug');

      // Check localStorage for subscription details
      const pendingSubscriptionData = localStorage.getItem('pendingSubscription');
      let pendingSubscription = null;
      
      if (pendingSubscriptionData) {
        try {
          pendingSubscription = JSON.parse(pendingSubscriptionData);
        } catch (e) {
          console.error('Error parsing pending subscription:', e);
        }
      }

      // Use subscription ID from URL params or localStorage
      const finalSubscriptionId = subscriptionId || pendingSubscription?.subscriptionId;
      const finalCommunityId = communityId || pendingSubscription?.communityId;
      const finalCommunitySlug = communitySlug || pendingSubscription?.communitySlug;

      console.log('Payment return params:', {
        subscriptionId: finalSubscriptionId,
        paymentId,
        hasSignature: !!signature,
        communityId: finalCommunityId,
        communitySlug: finalCommunitySlug,
        fromRazorpay: searchParams.get('source') === 'razorpay',
        allParams: Object.fromEntries(searchParams.entries())
      });

      if (!finalSubscriptionId) {
        setStatus('error');
        setMessage('Payment session not found. Please check your subscription status.');
        return;
      }

      try {
        // If we have all payment verification parameters, verify immediately
        if (paymentId && signature) {
          setMessage('Verifying payment signature...');
          
          const verifyResponse = await fetch('/api/community-subscriptions/verify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              subscriptionId: finalSubscriptionId,
              paymentId,
              signature,
              communityId: finalCommunityId
            }),
          });

          const verifyData = await verifyResponse.json();

          if (verifyResponse.ok && verifyData.success) {
            // Payment verified successfully
            localStorage.removeItem('pendingSubscription');
            setStatus('success');
            setMessage('Payment successful! Redirecting to your community...');
            setResult({
              success: true,
              message: 'Your subscription has been activated successfully.',
              subscription: verifyData.subscription
            });

            // Redirect after a short delay
            setTimeout(() => {
              if (finalCommunitySlug) {
                router.push(`/Newcompage/${finalCommunitySlug}?subscription=success`);
              } else if (finalCommunityId) {
                router.push(`/Newcompage/${finalCommunityId}?subscription=success`);
              } else {
                router.push('/admin/communities?subscription=success');
              }
            }, 2000);
            return;
          }
        }

        // Fallback: Poll subscription status (for cases without direct payment params)
        setMessage('Checking subscription status...');
        let pollCount = 0;
        const maxPolls = 24; // Poll for up to 2 minutes (24 * 5 seconds) - Credit card payments take longer
        
        const pollSubscriptionStatus = async (): Promise<void> => {
          try {
            const response = await fetch(`/api/community-subscriptions/status?subscriptionId=${finalSubscriptionId}`);
            const data = await response.json();

            if (response.ok && data.subscription?.status === 'active') {
              // Payment successful!
              localStorage.removeItem('pendingSubscription');
              setStatus('success');
              setMessage('Payment successful! Redirecting to your community...');
              setResult({
                success: true,
                message: 'Your subscription has been activated successfully.',
                subscription: data.subscription
              });
              
              // Redirect to community page
              setTimeout(() => {
                if (finalCommunitySlug) {
                  router.push(`/Newcompage/${finalCommunitySlug}?subscription=success`);
                } else if (finalCommunityId) {
                  router.push(`/Newcompage/${finalCommunityId}?subscription=success`);
                } else {
                  router.push('/admin/communities?subscription=success');
                }
              }, 2000);
              return;
            }

            pollCount++;
            if (pollCount >= maxPolls) {
              setStatus('error');
              setMessage('Payment verification is taking longer than expected. Your payment may still be processing. Please check your subscription status in your account.');
              return;
            }

            // Continue polling
            setTimeout(pollSubscriptionStatus, 5000); // Poll every 5 seconds
          } catch (error) {
            console.error('Error polling subscription status:', error);
            pollCount++;
            if (pollCount >= maxPolls) {
              setStatus('error');
              setMessage('Unable to verify payment status. Please check your subscription in your account.');
            } else {
              // Retry after a delay
              setTimeout(pollSubscriptionStatus, 5000);
            }
          }
        };

        // Start polling
        pollSubscriptionStatus();

      } catch (error: any) {
        console.error('Error in payment return handler:', error);
        setStatus('error');
        setMessage(error.message || 'An error occurred while verifying your payment.');
      }
    };

    if (session?.user) {
      handlePaymentReturn();
    }
  }, [searchParams, session, router]);

  const handleRetry = () => {
    localStorage.removeItem('pendingSubscription');
    router.push('/admin/communities');
  };

  const handleCheckStatus = () => {
    router.push('/admin/communities');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {status === 'processing' && (
          <>
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Processing Payment</h2>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="mt-6 bg-blue-50 rounded-lg p-4">
              <p className="text-sm text-blue-800">
                We're verifying your payment with Razorpay. This usually takes a few seconds.
              </p>
            </div>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="text-green-600 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-green-900 mb-2">Payment Successful!</h2>
            <p className="text-green-700 mb-4">{message}</p>
            {result && (
              <div className="bg-green-50 rounded-lg p-4 text-left">
                <h3 className="font-medium text-green-900 mb-2">Subscription Details:</h3>
                <p className="text-sm text-green-800">
                  Status: <span className="font-medium">Active</span>
                </p>
                <p className="text-sm text-green-800">
                  Subscription ID: <span className="font-mono text-xs">{result.subscription?.razorpaySubscriptionId}</span>
                </p>
              </div>
            )}
          </>
        )}

        {status === 'error' && (
          <>
            <div className="text-red-600 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-red-900 mb-2">Payment Verification</h2>
            <p className="text-red-700 mb-6">{message}</p>
            <div className="space-y-3">
              <button
                type="button"
                onClick={handleCheckStatus}
                className="w-full bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Check Subscription Status
              </button>
              <button
                type="button"
                onClick={handleRetry}
                className="w-full bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Go to Dashboard
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default function PaymentReturnPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading...</h2>
          <p className="text-gray-600">Please wait while we load your payment status.</p>
        </div>
      </div>
    }>
      <PaymentReturnContent />
    </Suspense>
  );
}
