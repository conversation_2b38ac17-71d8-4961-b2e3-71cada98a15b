import { createHmac } from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import { createR2Agent } from './r2-agent';

// Types for retry functionality
interface RetryResult<T> {
  data: T;
  retry: number;
}

interface RetryOptions {
  maxAttempts?: number;
  timeoutMs?: number;
  baseDelayMs?: number;
}

interface R2Config {
  accountId: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  publicUrl?: string;
}

interface UploadResult {
  url: string;
  key: string;
  fileName: string;
  fileType: string;
}

class R2Client {
  private config: R2Config;
  private httpsAgent: any;

  constructor(config: R2Config) {
    this.config = config;
    this.httpsAgent = createR2Agent();
  }

  /**
   * Create AWS Signature Version 4 for R2 requests
   */
  private createSignature(
    method: string,
    path: string,
    queryString: string = '',
    headers: Record<string, string> = {},
    body: string = ''
  ): Record<string, string> {
    const now = new Date();
    const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
    const dateStamp = amzDate.substr(0, 8);
    
    const region = 'auto';
    const service = 's3';
    
    // Create canonical headers
    const canonicalHeaders = Object.keys(headers)
      .sort()
      .map(key => `${key.toLowerCase()}:${headers[key]}`)
      .join('\n') + '\n';
    
    const signedHeaders = Object.keys(headers)
      .sort()
      .map(key => key.toLowerCase())
      .join(';');
    
    // Create canonical request
    const canonicalRequest = [
      method,
      path,
      queryString,
      canonicalHeaders,
      signedHeaders,
      this.sha256(body)
    ].join('\n');
    
    // Create string to sign
    const algorithm = 'AWS4-HMAC-SHA256';
    const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
    const stringToSign = [
      algorithm,
      amzDate,
      credentialScope,
      this.sha256(canonicalRequest)
    ].join('\n');
    
    // Calculate signature
    const signingKey = this.getSignatureKey(dateStamp, region, service);
    const signature = createHmac('sha256', signingKey)
      .update(stringToSign)
      .digest('hex');
    
    // Create authorization header
    const authorization = `${algorithm} Credential=${this.config.accessKeyId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
    
    return {
      ...headers,
      'Authorization': authorization,
      'X-Amz-Date': amzDate,
    };
  }

  private sha256(data: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private getSignatureKey(dateStamp: string, region: string, service: string): Buffer {
    const kDate = createHmac('sha256', 'AWS4' + this.config.secretAccessKey).update(dateStamp).digest();
    const kRegion = createHmac('sha256', kDate).update(region).digest();
    const kService = createHmac('sha256', kRegion).update(service).digest();
    const kSigning = createHmac('sha256', kService).update('aws4_request').digest();
    return kSigning;
  }

  /**
   * Custom fetch with better error handling and timeout
   * Uses undici for better SSL compatibility on Windows
   */
  private async fetchWithRetry(url: string, options: RequestInit): Promise<Response> {
    // Try multiple approaches for SSL compatibility
    const approaches = [
      {
        name: 'Built-in fetch',
        fn: () => this.fallbackFetch(url, options)
      },
      {
        name: 'Node-fetch with relaxed SSL',
        fn: async () => {
          if (typeof window !== 'undefined') {
            throw new Error('Client-side only');
          }

          const fetch = (await import('node-fetch')).default;
          const https = await import('https');

          // Try with relaxed SSL settings for Windows compatibility
          const agent = new https.Agent({
            keepAlive: true,
            timeout: 30000,
            // Disable SSL verification temporarily to test connectivity
            rejectUnauthorized: false,
            secureProtocol: 'TLSv1_2_method',
          });

          const response = await fetch(url, {
            ...options,
            agent,
            timeout: 30000,
          } as any);

          return response as unknown as Response;
        }
      }
    ];

    let lastError: any;

    for (const approach of approaches) {
      try {
        console.log(`Trying ${approach.name}...`);
        const response = await approach.fn();
        console.log(`✅ ${approach.name} succeeded`);
        return response;
      } catch (error: any) {
        console.log(`❌ ${approach.name} failed:`, error.message);
        lastError = error;

        // If it's an SSL error, continue to next approach
        if (error.code === 'ERR_SSL_SSLV3_ALERT_HANDSHAKE_FAILURE' ||
            error.code === 'EPROTO' ||
            error.message.includes('handshake failure')) {
          continue;
        }

        // For other errors, throw immediately
        throw error;
      }
    }

    // If all approaches failed, throw the last error
    throw lastError;
  }

  /**
   * Fallback fetch with timeout and error handling
   */
  private async fallbackFetch(url: string, options: RequestInit): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error: any) {
      clearTimeout(timeoutId);

      // Handle specific SSL errors
      if (error.code === 'ERR_SSL_SSLV3_ALERT_HANDSHAKE_FAILURE') {
        console.warn('SSL handshake failure detected:', error.message);
        throw new Error('SSL connection failed. Please check your network connection and try again.');
      }

      if (error.name === 'AbortError') {
        throw new Error('Request timeout. Please try again.');
      }

      throw error;
    }
  }

  /**
   * Retry with exponential backoff
   */
  private async retryWithBackoff<T>(
    action: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<RetryResult<T>> {
    const maxAttempts = options.maxAttempts || 3;
    const timeoutMs = options.timeoutMs || 30000;
    const baseDelayMs = options.baseDelayMs || 250;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const abortController = new AbortController();
      const timeout = setTimeout(() => abortController.abort(), timeoutMs);

      try {
        const data = await action();
        clearTimeout(timeout);
        return { data, retry: attempt };
      } catch (error) {
        clearTimeout(timeout);
        if (attempt === maxAttempts || abortController.signal.aborted) {
          throw error;
        }
        const delay = baseDelayMs * Math.pow(2, attempt - 1);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
    throw new Error('Retry attempts exceeded');
  }

  /**
   * Upload a file directly to R2
   */
  async uploadFile(
    file: Buffer,
    key: string,
    contentType: string,
    options: RetryOptions = {}
  ): Promise<RetryResult<UploadResult>> {
    return this.retryWithBackoff(async () => {
      // Try different endpoint formats for better compatibility
      const endpoints = [
        `https://${this.config.accountId}.r2.cloudflarestorage.com`,
        `https://s3.${this.config.accountId}.r2.cloudflarestorage.com`, // Alternative S3-style endpoint
      ];

      let lastError: any;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint}`);

          const path = `/${this.config.bucketName}/${key}`;
          const hostname = endpoint.replace('https://', '');

          const headers = {
            'Content-Type': contentType,
            'Content-Length': file.length.toString(),
            'Host': hostname,
          };

          const signedHeaders = this.createSignature('PUT', path, '', headers, file.toString());

          // Use a more robust fetch with better SSL handling
          const response = await this.fetchWithRetry(`${endpoint}${path}`, {
            method: 'PUT',
            headers: signedHeaders,
            body: file,
          });

          if (!response.ok) {
            const errorText = await response.text().catch(() => 'Unknown error');
            throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
          }

          console.log(`✅ Upload successful with endpoint: ${endpoint}`);
          return {
            url: this.getPublicUrl(key),
            key,
            fileName: key.split('/').pop() || key,
            fileType: contentType,
          };

        } catch (error: any) {
          console.log(`❌ Endpoint ${endpoint} failed:`, error.message);
          lastError = error;

          // If it's an SSL error, try next endpoint
          if (error.code === 'ERR_SSL_SSLV3_ALERT_HANDSHAKE_FAILURE' ||
              error.code === 'EPROTO' ||
              error.message.includes('handshake failure')) {
            continue;
          }

          // For other errors, throw immediately
          throw error;
        }
      }

      // If all endpoints failed, throw the last error
      throw lastError;
    }, options);
  }

  /**
   * Delete a file from R2
   */
  async deleteFile(key: string, options: RetryOptions = {}): Promise<RetryResult<boolean>> {
    return this.retryWithBackoff(async () => {
      const endpoint = `https://${this.config.accountId}.r2.cloudflarestorage.com`;
      const path = `/${this.config.bucketName}/${key}`;
      
      const headers = {
        'Host': `${this.config.accountId}.r2.cloudflarestorage.com`,
      };

      const signedHeaders = this.createSignature('DELETE', path, '', headers);

      const response = await this.fetchWithRetry(`${endpoint}${path}`, {
        method: 'DELETE',
        headers: signedHeaders,
      });

      if (!response.ok && response.status !== 404) {
        throw new Error(`Delete failed: ${response.status} ${response.statusText}`);
      }

      return true;
    }, options);
  }

  /**
   * Generate a presigned URL for uploading
   */
  generatePresignedUploadUrl(
    key: string,
    contentType: string,
    expiresIn: number = 3600
  ): string {
    try {
      const endpoint = `https://${this.config.accountId}.r2.cloudflarestorage.com`;
      const path = `/${this.config.bucketName}/${key}`;

      const now = new Date();
      const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
      const dateStamp = amzDate.substring(0, 8);

      const queryParams = new URLSearchParams({
        'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
        'X-Amz-Credential': `${this.config.accessKeyId}/${dateStamp}/auto/s3/aws4_request`,
        'X-Amz-Date': amzDate,
        'X-Amz-Expires': expiresIn.toString(),
        'X-Amz-SignedHeaders': 'host',
      });

      // Create canonical request for presigned URL
      const canonicalHeaders = `host:${this.config.accountId}.r2.cloudflarestorage.com\n`;
      const signedHeaders = 'host';

      const canonicalRequest = [
        'PUT',
        path,
        queryParams.toString(),
        canonicalHeaders,
        signedHeaders,
        'UNSIGNED-PAYLOAD'
      ].join('\n');

      // Create string to sign
      const algorithm = 'AWS4-HMAC-SHA256';
      const credentialScope = `${dateStamp}/auto/s3/aws4_request`;
      const stringToSign = [
        algorithm,
        amzDate,
        credentialScope,
        this.sha256(canonicalRequest)
      ].join('\n');

      // Calculate signature
      const signingKey = this.getSignatureKey(dateStamp, 'auto', 's3');
      const signature = require('crypto').createHmac('sha256', signingKey)
        .update(stringToSign)
        .digest('hex');

      const finalUrl = `${endpoint}${path}?${queryParams.toString()}&X-Amz-Signature=${signature}`;

      console.log('Generated presigned URL successfully');
      return finalUrl;

    } catch (error: any) {
      console.error('Error generating presigned URL:', error);
      throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  /**
   * Get public URL for a file
   */
  getPublicUrl(key: string): string {
    if (this.config.publicUrl) {
      return `${this.config.publicUrl}/${key}`;
    }
    
    // Fallback to the hardcoded public URL
    return `https://pub-895f71ea78c843b59c97073ccfe523c5.r2.dev/${key}`;
  }

  /**
   * Generate a unique file path
   */
  generateFilePath(fileName: string, folder: string = 'uploads'): string {
    const fileExtension = fileName.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    return `${folder}/${uniqueFileName}`;
  }
}

// Export a configured instance
export function createR2Client(): R2Client {
  const config: R2Config = {
    accountId: process.env.R2_ACCOUNT_ID || '',
    accessKeyId: process.env.R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '',
    bucketName: process.env.R2_BUCKET_NAME || '',
    publicUrl: process.env.NEXT_PUBLIC_R2_PUBLIC_URL || process.env.NEXT_PUBLIC_CUSTOM_DOMAIN,
  };

  return new R2Client(config);
}

export { R2Client, type RetryResult, type RetryOptions, type UploadResult };
