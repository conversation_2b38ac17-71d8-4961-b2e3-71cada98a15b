import { createHmac } from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import { createR2Agent } from './r2-agent';

// Types for retry functionality
interface RetryResult<T> {
  data: T;
  retry: number;
}

interface RetryOptions {
  maxAttempts?: number;
  timeoutMs?: number;
  baseDelayMs?: number;
}

interface R2Config {
  accountId: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  publicUrl?: string;
}

interface UploadResult {
  url: string;
  key: string;
  fileName: string;
  fileType: string;
}

class R2Client {
  private config: R2Config;
  private httpsAgent: any;

  constructor(config: R2Config) {
    this.config = config;
    this.httpsAgent = createR2Agent();
  }

  /**
   * Create AWS Signature Version 4 for R2 requests
   */
  private createSignature(
    method: string,
    path: string,
    queryString: string = '',
    headers: Record<string, string> = {},
    body: string = ''
  ): Record<string, string> {
    const now = new Date();
    const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
    const dateStamp = amzDate.substr(0, 8);
    
    const region = 'auto';
    const service = 's3';
    
    // Create canonical headers
    const canonicalHeaders = Object.keys(headers)
      .sort()
      .map(key => `${key.toLowerCase()}:${headers[key]}`)
      .join('\n') + '\n';
    
    const signedHeaders = Object.keys(headers)
      .sort()
      .map(key => key.toLowerCase())
      .join(';');
    
    // Create canonical request
    const canonicalRequest = [
      method,
      path,
      queryString,
      canonicalHeaders,
      signedHeaders,
      this.sha256(body)
    ].join('\n');
    
    // Create string to sign
    const algorithm = 'AWS4-HMAC-SHA256';
    const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
    const stringToSign = [
      algorithm,
      amzDate,
      credentialScope,
      this.sha256(canonicalRequest)
    ].join('\n');
    
    // Calculate signature
    const signingKey = this.getSignatureKey(dateStamp, region, service);
    const signature = createHmac('sha256', signingKey)
      .update(stringToSign)
      .digest('hex');
    
    // Create authorization header
    const authorization = `${algorithm} Credential=${this.config.accessKeyId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
    
    return {
      ...headers,
      'Authorization': authorization,
      'X-Amz-Date': amzDate,
    };
  }

  private sha256(data: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private getSignatureKey(dateStamp: string, region: string, service: string): Buffer {
    const kDate = createHmac('sha256', 'AWS4' + this.config.secretAccessKey).update(dateStamp).digest();
    const kRegion = createHmac('sha256', kDate).update(region).digest();
    const kService = createHmac('sha256', kRegion).update(service).digest();
    const kSigning = createHmac('sha256', kService).update('aws4_request').digest();
    return kSigning;
  }

  /**
   * Custom fetch with better error handling and timeout
   */
  private async fetchWithRetry(url: string, options: RequestInit): Promise<Response> {
    // Add timeout to the request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error: any) {
      clearTimeout(timeoutId);

      // Handle specific SSL errors
      if (error.code === 'ERR_SSL_SSLV3_ALERT_HANDSHAKE_FAILURE') {
        console.warn('SSL handshake failure, retrying with different approach...');
        // Try again without any special SSL configuration
        return fetch(url, options);
      }

      throw error;
    }
  }

  /**
   * Retry with exponential backoff
   */
  private async retryWithBackoff<T>(
    action: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<RetryResult<T>> {
    const maxAttempts = options.maxAttempts || 3;
    const timeoutMs = options.timeoutMs || 30000;
    const baseDelayMs = options.baseDelayMs || 250;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const abortController = new AbortController();
      const timeout = setTimeout(() => abortController.abort(), timeoutMs);

      try {
        const data = await action();
        clearTimeout(timeout);
        return { data, retry: attempt };
      } catch (error) {
        clearTimeout(timeout);
        if (attempt === maxAttempts || abortController.signal.aborted) {
          throw error;
        }
        const delay = baseDelayMs * Math.pow(2, attempt - 1);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
    throw new Error('Retry attempts exceeded');
  }

  /**
   * Upload a file directly to R2
   */
  async uploadFile(
    file: Buffer,
    key: string,
    contentType: string,
    options: RetryOptions = {}
  ): Promise<RetryResult<UploadResult>> {
    return this.retryWithBackoff(async () => {
      const endpoint = `https://${this.config.accountId}.r2.cloudflarestorage.com`;
      const path = `/${this.config.bucketName}/${key}`;
      
      const headers = {
        'Content-Type': contentType,
        'Content-Length': file.length.toString(),
        'Host': `${this.config.accountId}.r2.cloudflarestorage.com`,
      };

      const signedHeaders = this.createSignature('PUT', path, '', headers, file.toString());

      // Use a more robust fetch with better SSL handling
      const response = await this.fetchWithRetry(`${endpoint}${path}`, {
        method: 'PUT',
        headers: signedHeaders,
        body: file,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
      }

      return {
        url: this.getPublicUrl(key),
        key,
        fileName: key.split('/').pop() || key,
        fileType: contentType,
      };
    }, options);
  }

  /**
   * Delete a file from R2
   */
  async deleteFile(key: string, options: RetryOptions = {}): Promise<RetryResult<boolean>> {
    return this.retryWithBackoff(async () => {
      const endpoint = `https://${this.config.accountId}.r2.cloudflarestorage.com`;
      const path = `/${this.config.bucketName}/${key}`;
      
      const headers = {
        'Host': `${this.config.accountId}.r2.cloudflarestorage.com`,
      };

      const signedHeaders = this.createSignature('DELETE', path, '', headers);

      const response = await this.fetchWithRetry(`${endpoint}${path}`, {
        method: 'DELETE',
        headers: signedHeaders,
      });

      if (!response.ok && response.status !== 404) {
        throw new Error(`Delete failed: ${response.status} ${response.statusText}`);
      }

      return true;
    }, options);
  }

  /**
   * Generate a presigned URL for uploading
   */
  generatePresignedUploadUrl(
    key: string,
    contentType: string,
    expiresIn: number = 3600
  ): string {
    const endpoint = `https://${this.config.accountId}.r2.cloudflarestorage.com`;
    const path = `/${this.config.bucketName}/${key}`;
    
    const now = new Date();
    const expires = Math.floor(now.getTime() / 1000) + expiresIn;
    
    const queryParams = new URLSearchParams({
      'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
      'X-Amz-Credential': `${this.config.accessKeyId}/${now.toISOString().substr(0, 10).replace(/-/g, '')}/auto/s3/aws4_request`,
      'X-Amz-Date': now.toISOString().replace(/[:\-]|\.\d{3}/g, ''),
      'X-Amz-Expires': expiresIn.toString(),
      'X-Amz-SignedHeaders': 'host',
    });

    const headers = {
      'Host': `${this.config.accountId}.r2.cloudflarestorage.com`,
    };

    const signedHeaders = this.createSignature('PUT', path, queryParams.toString(), headers);
    
    return `${endpoint}${path}?${queryParams.toString()}&X-Amz-Signature=${signedHeaders['Authorization']?.split('Signature=')[1]}`;
  }

  /**
   * Get public URL for a file
   */
  getPublicUrl(key: string): string {
    if (this.config.publicUrl) {
      return `${this.config.publicUrl}/${key}`;
    }
    
    // Fallback to the hardcoded public URL
    return `https://pub-895f71ea78c843b59c97073ccfe523c5.r2.dev/${key}`;
  }

  /**
   * Generate a unique file path
   */
  generateFilePath(fileName: string, folder: string = 'uploads'): string {
    const fileExtension = fileName.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    return `${folder}/${uniqueFileName}`;
  }
}

// Export a configured instance
export function createR2Client(): R2Client {
  const config: R2Config = {
    accountId: process.env.R2_ACCOUNT_ID || '',
    accessKeyId: process.env.R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '',
    bucketName: process.env.R2_BUCKET_NAME || '',
    publicUrl: process.env.NEXT_PUBLIC_R2_PUBLIC_URL || process.env.NEXT_PUBLIC_CUSTOM_DOMAIN,
  };

  return new R2Client(config);
}

export { R2Client, type RetryResult, type RetryOptions, type UploadResult };
