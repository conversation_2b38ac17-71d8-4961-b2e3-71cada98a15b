import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { CommunitySubscription } from "@/models/Subscription";

// GET /api/community-subscriptions/recent - Get recent subscriptions for current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Find recent subscriptions for this user (last 24 hours)
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    const subscriptions = await CommunitySubscription.find({
      adminId: session.user.id,
      createdAt: { $gte: twentyFourHoursAgo }
    })
    .populate('communityId', 'name slug')
    .sort({ createdAt: -1 })
    .limit(5)
    .lean();

    // Format the response
    const formattedSubscriptions = subscriptions.map((sub: any) => ({
      id: sub._id,
      razorpaySubscriptionId: sub.razorpaySubscriptionId,
      status: sub.status,
      communityId: sub.communityId?._id,
      communityName: sub.communityId?.name,
      communitySlug: sub.communityId?.slug,
      createdAt: sub.createdAt,
      updatedAt: sub.updatedAt,
      paidCount: sub.paidCount,
      amount: sub.amount
    }));

    return NextResponse.json({
      success: true,
      subscriptions: formattedSubscriptions
    });

  } catch (error: any) {
    console.error("Error fetching recent subscriptions:", error);
    return NextResponse.json(
      { error: "Failed to fetch recent subscriptions" },
      { status: 500 }
    );
  }
}
