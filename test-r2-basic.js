/**
 * Basic connectivity test for R2 endpoint
 */

const https = require('https');
const http = require('http');

const R2_ACCOUNT_ID = 'e6e8f16fca044c21183dbe6f5007951f';

async function testBasicConnectivity() {
  console.log('Testing basic connectivity to R2...');
  
  const endpoints = [
    `${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    'cloudflare.com', // Test if we can reach Cloudflare at all
    'google.com' // Test general internet connectivity
  ];
  
  for (const endpoint of endpoints) {
    console.log(`\nTesting ${endpoint}...`);
    
    try {
      const result = await new Promise((resolve, reject) => {
        const req = https.request({
          hostname: endpoint,
          path: '/',
          method: 'HEAD',
          timeout: 10000,
          // Try with minimal SSL requirements
          rejectUnauthorized: false,
        }, (res) => {
          console.log(`✅ ${endpoint} - Status: ${res.statusCode}`);
          console.log(`   Headers:`, Object.keys(res.headers).slice(0, 5));
          resolve({ statusCode: res.statusCode, headers: res.headers });
        });
        
        req.on('error', (error) => {
          console.log(`❌ ${endpoint} - Error: ${error.message}`);
          if (error.code) {
            console.log(`   Error code: ${error.code}`);
          }
          reject(error);
        });
        
        req.on('timeout', () => {
          console.log(`❌ ${endpoint} - Timeout`);
          req.destroy();
          reject(new Error('Timeout'));
        });
        
        req.end();
      });
      
    } catch (error) {
      // Error already logged above
    }
  }
}

async function testWithCurl() {
  console.log('\n\nTesting with system curl (if available)...');
  
  const { spawn } = require('child_process');
  
  try {
    const result = await new Promise((resolve, reject) => {
      const curl = spawn('curl', [
        '-I', // HEAD request
        '-v', // Verbose
        '--connect-timeout', '10',
        '--max-time', '30',
        `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com/`
      ]);
      
      let output = '';
      let errorOutput = '';
      
      curl.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      curl.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });
      
      curl.on('close', (code) => {
        console.log(`Curl exit code: ${code}`);
        console.log('Curl output:', output);
        console.log('Curl errors:', errorOutput);
        resolve({ code, output, errorOutput });
      });
      
      curl.on('error', (error) => {
        console.log('Curl not available:', error.message);
        reject(error);
      });
    });
  } catch (error) {
    console.log('Could not run curl test');
  }
}

async function testNodeVersion() {
  console.log('\n\nNode.js and OpenSSL information:');
  console.log(`Node.js version: ${process.version}`);
  console.log(`Platform: ${process.platform}`);
  console.log(`Architecture: ${process.arch}`);
  
  // Check OpenSSL version
  try {
    const crypto = require('crypto');
    console.log(`OpenSSL version: ${process.versions.openssl}`);
    
    // Check supported TLS versions
    const tls = require('tls');
    console.log('Supported TLS versions:', tls.DEFAULT_MIN_VERSION, 'to', tls.DEFAULT_MAX_VERSION);
    
    // Check available ciphers
    const ciphers = crypto.constants;
    console.log('SSL constants available:', Object.keys(ciphers).filter(k => k.includes('SSL')).length > 0);
    
  } catch (error) {
    console.log('Could not get OpenSSL info:', error.message);
  }
}

async function runAllTests() {
  await testNodeVersion();
  await testBasicConnectivity();
  await testWithCurl();
}

runAllTests().catch(console.error);
