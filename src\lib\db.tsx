import mongoose from "mongoose";

// Get the MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI;

const isDevelopment = process.env.NODE_ENV === "development";
const isBuildTime = process.env.NEXT_PHASE === "phase-production-build";

// Only throw an error if we're not in the build phase and MongoDB URI is missing
if (!MONGODB_URI && !isBuildTime) {
  console.warn("MongoDB URI is not defined - database connections will fail");
}

// Define a type for our cached connection
type MongooseCache = {
  conn: mongoose.Connection | null;
  promise: Promise<mongoose.Connection> | null;
};

// Use a module-level variable instead of global
let cached: MongooseCache = { conn: null, promise: null };

export async function dbconnect() {
  // If we're in the build phase and no MongoDB URI is available, return a mock connection
  if (isBuildTime && !MONGODB_URI) {
    console.warn(
      "Build phase detected with no MongoDB URI - returning mock connection"
    );
    return {
      readyState: 0,
      models: {},
      on: () => {},
      once: () => {},
    } as unknown as mongoose.Connection;
  }

  // Return cached connection if available and still connected
  if (cached.conn && cached.conn.readyState === 1) {
    return cached.conn;
  }

  // Check for MongoDB URI before attempting connection
  if (!MONGODB_URI) {
    throw new Error("Cannot connect to MongoDB: URI is not defined");
  }

  // Create a new connection if none exists or if the promise failed
  if (!cached.promise) {
    const opts = {
      bufferCommands: false, // Disable mongoose buffering
      maxPoolSize: 10, // Reduced pool size for stability
      minPoolSize: 1, // Minimum connections
      serverSelectionTimeoutMS: 15000, // Increased timeout for DNS resolution
      socketTimeoutMS: 45000, // Increased socket timeout
      connectTimeoutMS: 15000, // Increased connection timeout for slow networks
      maxIdleTimeMS: 30000, // Increased idle time
      retryWrites: true,
      authSource: "admin", // Specify auth source
      heartbeatFrequencyMS: 30000, // Less frequent heartbeats to reduce load
      // Additional options for better connectivity
      family: 4, // Force IPv4 to avoid IPv6 issues
      directConnection: false, // Use SRV record for Atlas
      ssl: true, // Ensure SSL is enabled
      retryReads: true, // Enable retry reads
      readPreference: 'primary' as const, // Read from primary for consistency
    };

    console.log("Attempting to connect to MongoDB...");
    console.log(
      "MongoDB URI pattern:",
      MONGODB_URI.replace(/:\/\/[^@]+@/, "://***:***@")
    );

    // Retry logic with exponential backoff
    const connectWithRetry = async (retryCount = 0): Promise<mongoose.Connection> => {
      const maxRetries = 3;
      const baseDelay = 2000; // 2 seconds

      try {
        const connection = await mongoose.connect(MONGODB_URI, opts);
        console.log("MongoDB connected successfully");
        return mongoose.connection;
      } catch (error: any) {
        console.error(`MongoDB connection attempt ${retryCount + 1} failed:`, error.message);

        if (retryCount < maxRetries) {
          const delay = baseDelay * Math.pow(2, retryCount); // Exponential backoff
          console.log(`Retrying MongoDB connection in ${delay}ms... (attempt ${retryCount + 2}/${maxRetries + 1})`);

          await new Promise(resolve => setTimeout(resolve, delay));
          return connectWithRetry(retryCount + 1);
        } else {
          // All retries exhausted
          console.error("All MongoDB connection retries exhausted");

          // Provide specific error messages for common issues
          if (error.message.includes("ECONNREFUSED")) {
            console.error("🔧 TROUBLESHOOTING GUIDE - CONNECTION REFUSED:");
            console.error("1. Check if MongoDB Atlas cluster is active (not paused)");
            console.error("2. Verify Network Access settings in MongoDB Atlas");
            console.error("3. Add your IP address (0.0.0.0/0 for development) to the IP Access List");
            console.error("4. Ensure cluster is in the correct region");
          } else if (error.message.includes("ETIMEOUT") || error.message.includes("querySrv")) {
            console.error("🔧 TROUBLESHOOTING GUIDE - DNS/TIMEOUT:");
            console.error("1. Check your internet connection stability");
            console.error("2. Try using a different DNS server (*******, *******)");
            console.error("3. Disable VPN if active");
            console.error("4. Check if your firewall is blocking MongoDB connections");
            console.error("5. Try connecting from a different network");
            console.error("6. Consider using a direct connection string instead of SRV");
          }

          if (error.message.includes("Authentication failed")) {
            console.error(
              "Authentication issue - check username/password in connection string"
            );
          }

          // Reset promise on failure so we can retry
          cached.promise = null;
          throw error;
        }
      }
    };

    try {
      cached.promise = connectWithRetry();
    } catch (error: any) {
      console.error("Error setting up MongoDB connection:", error);
      // Reset promise on failure so we can retry
      cached.promise = null;
      throw new Error(`Failed to connect to MongoDB: ${error.message}`);
    }
  }

  try {
    cached.conn = await cached.promise;

    // Set up connection event listeners
    if (cached.conn) {
      cached.conn.on("disconnected", () => {
        console.warn("MongoDB disconnected");
        cached.conn = null;
        cached.promise = null;
      });

      cached.conn.on("error", (error) => {
        console.error("MongoDB connection error:", error);
        cached.conn = null;
        cached.promise = null;
      });

      cached.conn.on("reconnected", () => {
        console.log("MongoDB reconnected");
      });
    }
  } catch (error) {
    console.error("Error resolving MongoDB connection:", error);
    // Reset both promise and connection on failure
    cached.promise = null;
    cached.conn = null;
    throw new Error(`Failed to resolve MongoDB connection: ${error}`);
  }

  return cached.conn;
}
