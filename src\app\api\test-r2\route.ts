import { NextRequest, NextResponse } from "next/server";
import { createR2Client } from "@/lib/r2-client";

// Initialize R2 client
const r2Client = createR2Client();

export async function GET() {
  try {
    // Test R2 configuration
    const configCheck = {
      hasAccountId: !!process.env.R2_ACCOUNT_ID,
      hasAccessKey: !!process.env.R2_ACCESS_KEY_ID,
      hasSecretKey: !!process.env.R2_SECRET_ACCESS_KEY,
      hasBucketName: !!process.env.R2_BUCKET_NAME,
      hasPublicUrl: !!process.env.NEXT_PUBLIC_R2_PUBLIC_URL,
      bucketName: process.env.R2_BUCKET_NAME,
      publicUrl: process.env.NEXT_PUBLIC_R2_PUBLIC_URL,
    };

    return NextResponse.json({
      success: true,
      config: configCheck,
      message: "R2 configuration test completed successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("R2 test error:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
