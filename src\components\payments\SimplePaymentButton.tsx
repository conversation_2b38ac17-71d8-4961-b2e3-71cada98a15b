"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import type { RazorpayOptions, RazorpayPaymentResponse } from "@/types/razorpay";

interface SimplePaymentButtonProps {
  communityId?: string;
  communitySlug?: string;
  buttonText?: string;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  className?: string;
  disabled?: boolean;
}

const SimplePaymentButton: React.FC<SimplePaymentButtonProps> = ({
  communityId,
  communitySlug,
  buttonText = "Pay Now ($29/month)",
  onSuccess,
  onError,
  className = "",
  disabled = false
}) => {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);

  // Load Razorpay script
  useEffect(() => {
    const loadRazorpayScript = () => {
      return new Promise((resolve) => {
        if (window.Razorpay) {
          setIsScriptLoaded(true);
          resolve(true);
          return;
        }

        const script = document.createElement("script");
        script.src = "https://checkout.razorpay.com/v1/checkout.js";
        script.onload = () => {
          setIsScriptLoaded(true);
          resolve(true);
        };
        script.onerror = () => {
          setError("Failed to load payment gateway");
          resolve(false);
        };
        document.body.appendChild(script);
      });
    };

    loadRazorpayScript();
  }, []);

  const handlePayment = async () => {
    console.log('🔄 Payment button clicked');
    console.log('Session:', session?.user ? 'Authenticated' : 'Not authenticated');
    console.log('Script loaded:', isScriptLoaded);
    console.log('Razorpay key:', process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID ? 'Set' : 'Not set');
    console.log('Community ID:', communityId);
    console.log('Community Slug:', communitySlug);

    if (!session?.user) {
      console.error('❌ User not authenticated');
      setError("You must be logged in to make a payment");
      return;
    }

    if (!isScriptLoaded) {
      console.error('❌ Razorpay script not loaded');
      setError("Payment gateway is not ready. Please try again.");
      return;
    }

    if (!process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID) {
      console.error('❌ Razorpay key not found');
      setError("Razorpay configuration error: Public key not found");
      return;
    }

    console.log('✅ All checks passed, creating subscription...');
    setIsLoading(true);
    setError(null);

    try {
      // Create community subscription
      console.log('📡 Making API call to create subscription...');
      console.log('Community ID:', communityId);
      console.log('Admin ID:', session.user.id);

      const response = await fetch("/api/community-subscriptions/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          communityId,
          adminId: session.user.id,
          customerNotify: true,
          notes: {
            planName: "Community Management Plan",
            adminEmail: session.user.email,
            communityId: communityId || "new"
          }
        }),
      });

      console.log('📡 API response status:', response.status);
      const data = await response.json();
      console.log('📡 API response data:', data);

      if (!response.ok) {
        console.error('❌ API call failed:', data);
        throw new Error(data.error || "Failed to create subscription");
      }

      const { subscription, razorpaySubscription, shortUrl } = data;

      console.log('🔍 Subscription creation result:', {
        subscriptionStatus: subscription?.status,
        razorpayStatus: razorpaySubscription?.status,
        hasShortUrl: !!shortUrl,
        razorpayId: razorpaySubscription?.id
      });

      // For SimplePaymentButton, always use modal (not hosted checkout)
      // Check if subscription requires authentication
      if (razorpaySubscription.status === "created") {
        console.log('🎯 Opening Razorpay modal for subscription authentication...');
        
        const options: RazorpayOptions = {
          key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
          subscription_id: razorpaySubscription.id,
          name: "TheTribeLab",
          description: "Community Management Plan - $29/month",
          handler: async function (response: RazorpayPaymentResponse) {
            console.log("🎉 Payment completed! Response:", {
              subscriptionId: response.razorpay_subscription_id,
              paymentId: response.razorpay_payment_id,
              hasSignature: !!response.razorpay_signature
            });

            await verifyPayment(
              response.razorpay_subscription_id!,
              response.razorpay_payment_id!,
              response.razorpay_signature!
            );
          },
          prefill: {
            name: session.user.name || "",
            email: session.user.email || "",
          },
          theme: {
            color: "#F37021",
          },
          modal: {
            ondismiss: function() {
              console.log("⚠️ Payment modal dismissed by user");
              setIsLoading(false);
            },
            confirm_close: true,
            escape: false
          },
          retry: {
            enabled: true,
            max_count: 3
          },
          timeout: 300, // 5 minutes
          remember_customer: false
        };

        console.log('🚀 Opening Razorpay modal with options:', {
          key: options.key?.substring(0, 10) + '...',
          subscription_id: options.subscription_id,
          name: options.name
        });

        const razorpay = new window.Razorpay(options);
        razorpay.open();
      } else {
        // Subscription created successfully
        onSuccess?.(subscription);
        // Redirect to community slug URL
        if (communitySlug) {
          router.push(`/Newcompage/${communitySlug}?subscription=activated`);
        } else if (communityId) {
          router.push(`/Newcompage/${communityId}?subscription=activated`);
        }
      }
    } catch (error: any) {
      console.error("❌ Payment error:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString()
      });

      const errorMessage = error.message || "An error occurred during payment";
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Verify payment with immediate verification (no long timeouts)
  const verifyPayment = async (
    subscriptionId: string,
    paymentId: string,
    signature: string
  ) => {
    try {
      console.log('🔄 Starting payment verification:', {
        subscriptionId: subscriptionId.substring(0, 15) + '...',
        paymentId: paymentId.substring(0, 15) + '...',
        hasSignature: !!signature
      });

      setIsLoading(true);
      setError(null);

      // Use shorter timeout for modal payments (they should be immediate)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const verifyResponse = await fetch("/api/community-subscriptions/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          subscriptionId,
          paymentId,
          signature,
          communityId
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const verifyData = await verifyResponse.json();

      console.log('✅ Verification response:', {
        success: verifyResponse.ok,
        status: verifyResponse.status,
        hasSubscription: !!verifyData.subscription
      });

      if (verifyResponse.ok && verifyData.subscription) {
        // Payment verified successfully
        console.log('🎉 Payment verification successful!');
        onSuccess?.(verifyData.subscription);

        // Redirect to community page
        if (communitySlug) {
          console.log('🔄 Redirecting to community:', `/Newcompage/${communitySlug}`);
          router.push(`/Newcompage/${communitySlug}?subscription=success`);
        } else if (communityId) {
          router.push(`/Newcompage/${communityId}?subscription=success`);
        } else {
          router.push('/admin/communities?subscription=success');
        }
      } else {
        // Verification failed
        console.error('❌ Verification failed:', verifyData);
        throw new Error(verifyData.error || "Payment verification failed");
      }
    } catch (error: any) {
      console.error('❌ Payment verification error:', error);

      if (error.name === 'AbortError') {
        setError("Verification timeout. Your payment may still be processing. Please check your subscription status.");
      } else {
        setError(error.message || "Payment verification failed");
      }

      onError?.(error.message || "Payment verification failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="simple-payment-button w-full max-w-full">
      {error && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-center">
          <p className="text-red-600 text-xs break-words">{error}</p>
        </div>
      )}

      <button
        type="button"
        onClick={handlePayment}
        disabled={disabled || isLoading || !isScriptLoaded}
        className={`${className} ${disabled || isLoading || !isScriptLoaded ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </div>
        ) : !isScriptLoaded ? (
          "Loading..."
        ) : (
          buttonText
        )}
      </button>
    </div>
  );
};

export default SimplePaymentButton;
