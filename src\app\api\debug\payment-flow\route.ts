import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { CommunitySubscription, ICommunitySubscription } from "@/models/Subscription";

// GET /api/debug/payment-flow - Debug payment flow issues
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Get recent subscriptions for this user
    const recentSubscriptions = await CommunitySubscription.find({
      adminId: session.user.id
    })
    .sort({ createdAt: -1 })
    .limit(5)
    .lean();

    // Check environment variables
    const envCheck = {
      razorpayKeyId: !!process.env.RAZORPAY_KEY_ID,
      razorpayKeySecret: !!process.env.RAZORPAY_KEY_SECRET,
      razorpayWebhookSecret: !!process.env.RAZORPAY_WEBHOOK_SECRET,
      nextAuthUrl: !!process.env.NEXTAUTH_URL,
      publicRazorpayKey: !!process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID
    };

    // Check webhook events for recent subscriptions
    const webhookEvents = recentSubscriptions.map((sub: ICommunitySubscription) => ({
      subscriptionId: sub.razorpaySubscriptionId,
      status: sub.status,
      webhookEvents: sub.webhookEvents || [],
      lastWebhookAt: sub.lastWebhookAt,
      createdAt: sub.createdAt
    }));

    return NextResponse.json({
      success: true,
      debug: {
        userId: session.user.id,
        userEmail: session.user.email,
        environment: envCheck,
        recentSubscriptions: recentSubscriptions.length,
        webhookEvents,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error("Debug endpoint error:", error);
    return NextResponse.json(
      { error: "Debug failed", details: error.message },
      { status: 500 }
    );
  }
}

// POST /api/debug/payment-flow - Test payment verification
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { action, subscriptionId } = await request.json();

    await dbconnect();

    if (action === "test_verification" && subscriptionId) {
      // Test subscription lookup
      const subscription = await CommunitySubscription.findOne({
        razorpaySubscriptionId: subscriptionId,
        adminId: session.user.id
      });

      return NextResponse.json({
        success: true,
        test: {
          subscriptionFound: !!subscription,
          subscriptionStatus: subscription?.status,
          webhookEvents: subscription?.webhookEvents?.length || 0,
          lastWebhookAt: subscription?.lastWebhookAt
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: "Debug test completed"
    });

  } catch (error: any) {
    console.error("Debug test error:", error);
    return NextResponse.json(
      { error: "Debug test failed", details: error.message },
      { status: 500 }
    );
  }
}
