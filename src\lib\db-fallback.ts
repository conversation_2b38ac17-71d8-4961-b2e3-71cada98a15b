import mongoose from "mongoose";

// Alternative MongoDB connection with direct connection (no SRV)
// Use this if the main connection continues to fail

const MONGODB_URI_FALLBACK = process.env.MONGODB_URI_FALLBACK || process.env.MONGODB_URI;

type MongooseCache = {
  conn: mongoose.Connection | null;
  promise: Promise<mongoose.Connection> | null;
};

let fallbackCached: MongooseCache = { conn: null, promise: null };

export async function dbconnectFallback() {
  // Return cached connection if available and still connected
  if (fallbackCached.conn && fallbackCached.conn.readyState === 1) {
    return fallbackCached.conn;
  }

  if (!MONGODB_URI_FALLBACK) {
    throw new Error("Cannot connect to MongoDB: Fallback URI is not defined");
  }

  if (!fallbackCached.promise) {
    // Alternative connection options for problematic networks
    const opts = {
      bufferCommands: false,
      maxPoolSize: 5, // Smaller pool for fallback
      minPoolSize: 1,
      serverSelectionTimeoutMS: 30000, // Longer timeout
      socketTimeoutMS: 60000, // Longer socket timeout
      connectTimeoutMS: 30000, // Longer connection timeout
      maxIdleTimeMS: 60000,
      retryWrites: true,
      authSource: "admin",
      heartbeatFrequencyMS: 60000, // Less frequent heartbeats
      // Force specific connection options
      family: 4, // IPv4 only
      ssl: true,
      retryReads: true,
      readPreference: 'primaryPreferred' as const,
      // Additional fallback options
      compressors: ['zlib' as const], // Enable compression
      zlibCompressionLevel: 6 as const,
    };

    console.log("🔄 Attempting fallback MongoDB connection...");
    
    // Convert SRV to direct connection if needed
    let connectionUri = MONGODB_URI_FALLBACK;
    if (connectionUri.includes('mongodb+srv://')) {
      // For fallback, you might want to use direct connection
      // This would require knowing the actual cluster endpoints
      console.log("Using SRV connection for fallback");
    }

    fallbackCached.promise = mongoose
      .connect(connectionUri, opts)
      .then(() => {
        console.log("✅ Fallback MongoDB connected successfully");
        return mongoose.connection;
      })
      .catch((error) => {
        console.error("❌ Fallback MongoDB connection failed:", error.message);
        fallbackCached.promise = null;
        throw error;
      });
  }

  try {
    fallbackCached.conn = await fallbackCached.promise;

    // Set up connection event listeners
    if (fallbackCached.conn) {
      fallbackCached.conn.on("disconnected", () => {
        console.warn("Fallback MongoDB disconnected");
        fallbackCached.conn = null;
        fallbackCached.promise = null;
      });

      fallbackCached.conn.on("error", (error) => {
        console.error("Fallback MongoDB connection error:", error);
        fallbackCached.conn = null;
        fallbackCached.promise = null;
      });
    }

    return fallbackCached.conn;
  } catch (error: any) {
    console.error("Error resolving fallback MongoDB connection:", error);
    fallbackCached.promise = null;
    fallbackCached.conn = null;
    throw new Error(`Failed to resolve fallback MongoDB connection: ${error}`);
  }
}

// Test connection function
export async function testMongoConnection() {
  try {
    console.log("🧪 Testing MongoDB connection...");
    const connection = await dbconnectFallback();
    
    // Test a simple operation
    if (!connection.db) {
      throw new Error('Database connection not established');
    }
    const admin = connection.db.admin();
    const result = await admin.ping();
    
    console.log("✅ MongoDB connection test successful:", result);
    return { success: true, result };
  } catch (error: any) {
    console.error("❌ MongoDB connection test failed:", error.message);
    return { success: false, error: error.message };
  }
}
