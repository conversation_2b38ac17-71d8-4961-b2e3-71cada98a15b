import { NextRequest, NextResponse } from "next/server";

// POST /api/test-webhook - Test webhook endpoint to verify webhook functionality
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get("x-razorpay-signature");
    
    console.log("🔔 Test webhook received:", {
      hasBody: !!body,
      bodyLength: body.length,
      hasSignature: !!signature,
      timestamp: new Date().toISOString(),
      headers: Object.fromEntries(request.headers.entries())
    });

    if (body) {
      try {
        const event = JSON.parse(body);
        console.log("📦 Webhook event:", {
          event: event.event,
          entity: event.payload?.subscription?.entity?.id || event.payload?.payment?.entity?.id,
          timestamp: new Date().toISOString()
        });
      } catch (parseError) {
        console.log("❌ Failed to parse webhook body:", parseError);
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: "Test webhook received",
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error("❌ Test webhook error:", error);
    return NextResponse.json(
      { error: "Test webhook failed", details: error.message },
      { status: 500 }
    );
  }
}

// GET /api/test-webhook - Test webhook endpoint info
export async function GET() {
  return NextResponse.json({
    message: "Test webhook endpoint is active",
    timestamp: new Date().toISOString(),
    environment: {
      webhookSecret: !!process.env.RAZORPAY_WEBHOOK_SECRET,
      razorpayKey: !!process.env.RAZORPAY_KEY_ID
    }
  });
}
