import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const keyId = process.env.RAZORPAY_KEY_ID;
    const keySecret = process.env.RAZORPAY_KEY_SECRET;
    const planId = process.env.RAZORPAY_COMMUNITY_PLAN_ID;

    if (!keyId || !keySecret) {
      return NextResponse.json({
        error: "Razorpay credentials not configured",
        details: {
          hasKeyId: !!keyId,
          hasKeySecret: !!keySecret,
          keyIdLength: keyId?.length || 0,
          keySecretLength: keySecret?.length || 0
        }
      }, { status: 500 });
    }

    // Test API connection by fetching the plan
    const auth = Buffer.from(`${keyId}:${keySecret}`).toString("base64");
    
    const response = await fetch(`https://api.razorpay.com/v1/plans/${planId}`, {
      method: "GET",
      headers: {
        Authorization: `Basic ${auth}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json({
        error: "Razorpay API test failed",
        status: response.status,
        details: errorData
      }, { status: response.status });
    }

    const planData = await response.json();

    return NextResponse.json({
      success: true,
      message: "Razorpay configuration is working",
      plan: {
        id: planData.id,
        amount: planData.amount,
        currency: planData.currency,
        interval: planData.interval,
        period: planData.period
      },
      config: {
        keyId: keyId.substring(0, 8) + '...',
        environment: keyId.startsWith('rzp_test_') ? 'test' : 'live'
      }
    });

  } catch (error: any) {
    console.error("Razorpay test error:", error);
    return NextResponse.json({
      error: "Razorpay test failed",
      message: error.message
    }, { status: 500 });
  }
}
