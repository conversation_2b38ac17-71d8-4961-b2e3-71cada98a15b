/**
 * Test R2 API configuration and credentials
 */

const crypto = require('crypto');

// R2 configuration
const R2_ACCOUNT_ID = 'e6e8f16fca044c21183dbe6f5007951f';
const R2_ACCESS_KEY_ID = 'e8c1709286897de45a17e5de2984653f';
const R2_SECRET_ACCESS_KEY = 'b61b9bc1071a3fe5f28066fbcdba47c2bf18fb281a54d4810046b7b666c108db';
const R2_BUCKET_NAME = 'thetribelab';

console.log('Testing R2 API Configuration...');
console.log('================================');

// Test 1: Check if credentials look valid
console.log('\n1. Credential Format Check:');
console.log(`Account ID: ${R2_ACCOUNT_ID} (${R2_ACCOUNT_ID.length} chars)`);
console.log(`Access Key: ${R2_ACCESS_KEY_ID} (${R2_ACCESS_KEY_ID.length} chars)`);
console.log(`Secret Key: ${R2_SECRET_ACCESS_KEY.substring(0, 8)}... (${R2_SECRET_ACCESS_KEY.length} chars)`);
console.log(`Bucket: ${R2_BUCKET_NAME}`);

// Validate format
const accountIdValid = /^[a-f0-9]{32}$/.test(R2_ACCOUNT_ID);
const accessKeyValid = /^[a-f0-9]{32}$/.test(R2_ACCESS_KEY_ID);
const secretKeyValid = /^[a-f0-9]{64}$/.test(R2_SECRET_ACCESS_KEY);

console.log(`Account ID format: ${accountIdValid ? '✅ Valid' : '❌ Invalid'}`);
console.log(`Access Key format: ${accessKeyValid ? '✅ Valid' : '❌ Invalid'}`);
console.log(`Secret Key format: ${secretKeyValid ? '✅ Valid' : '❌ Invalid'}`);

// Test 2: Check different endpoint formats
console.log('\n2. Testing Different R2 Endpoint Formats:');

const endpointFormats = [
  `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  `https://s3.${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com/${R2_BUCKET_NAME}`,
  `https://r2.cloudflarestorage.com/${R2_ACCOUNT_ID}`,
];

endpointFormats.forEach((endpoint, index) => {
  console.log(`Format ${index + 1}: ${endpoint}`);
});

// Test 3: Test signature generation
console.log('\n3. Testing AWS Signature Generation:');

function createTestSignature() {
  try {
    const now = new Date();
    const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
    const dateStamp = amzDate.substring(0, 8);
    
    const region = 'auto';
    const service = 's3';
    
    // Test signature key generation
    const kDate = crypto.createHmac('sha256', 'AWS4' + R2_SECRET_ACCESS_KEY).update(dateStamp).digest();
    const kRegion = crypto.createHmac('sha256', kDate).update(region).digest();
    const kService = crypto.createHmac('sha256', kRegion).update(service).digest();
    const kSigning = crypto.createHmac('sha256', kService).update('aws4_request').digest();
    
    console.log('✅ Signature generation successful');
    console.log(`Date stamp: ${dateStamp}`);
    console.log(`AMZ Date: ${amzDate}`);
    
    return true;
  } catch (error) {
    console.log('❌ Signature generation failed:', error.message);
    return false;
  }
}

createTestSignature();

// Test 4: Check if bucket name is valid
console.log('\n4. Bucket Name Validation:');
const bucketNameValid = /^[a-z0-9][a-z0-9\-]*[a-z0-9]$/.test(R2_BUCKET_NAME) && 
                       R2_BUCKET_NAME.length >= 3 && 
                       R2_BUCKET_NAME.length <= 63;

console.log(`Bucket name "${R2_BUCKET_NAME}": ${bucketNameValid ? '✅ Valid format' : '❌ Invalid format'}`);

// Test 5: Test with a simple HTTP request to check if the endpoint exists
console.log('\n5. Testing Endpoint Accessibility:');

async function testEndpointAccessibility() {
  const https = require('https');
  
  // Test if we can at least resolve the hostname
  const dns = require('dns').promises;
  
  try {
    const hostname = `${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`;
    console.log(`Testing DNS resolution for: ${hostname}`);
    
    const addresses = await dns.lookup(hostname);
    console.log(`✅ DNS resolution successful: ${addresses.address}`);
    
    // Test if the endpoint responds (even with an error is fine)
    return new Promise((resolve) => {
      const req = https.request({
        hostname: hostname,
        path: '/',
        method: 'HEAD',
        timeout: 5000,
        // Disable SSL verification for this test
        rejectUnauthorized: false,
      }, (res) => {
        console.log(`✅ Endpoint responds with status: ${res.statusCode}`);
        console.log(`Headers: ${Object.keys(res.headers).join(', ')}`);
        resolve(true);
      });
      
      req.on('error', (error) => {
        if (error.code === 'EPROTO' || error.message.includes('handshake')) {
          console.log('✅ Endpoint exists but has SSL handshake issues (expected)');
          console.log('   This confirms the endpoint is valid but has SSL compatibility problems');
        } else {
          console.log(`❌ Endpoint error: ${error.message}`);
        }
        resolve(false);
      });
      
      req.on('timeout', () => {
        console.log('❌ Endpoint timeout');
        req.destroy();
        resolve(false);
      });
      
      req.end();
    });
    
  } catch (error) {
    console.log(`❌ DNS resolution failed: ${error.message}`);
    return false;
  }
}

// Test 6: Check if credentials might be expired or invalid
console.log('\n6. Credential Validity Indicators:');

// Check if the access key and secret key are related (they should have some correlation)
const accessKeyHash = crypto.createHash('md5').update(R2_ACCESS_KEY_ID).digest('hex');
const secretKeyHash = crypto.createHash('md5').update(R2_SECRET_ACCESS_KEY).digest('hex');

console.log(`Access key hash: ${accessKeyHash.substring(0, 8)}...`);
console.log(`Secret key hash: ${secretKeyHash.substring(0, 8)}...`);

// Run the endpoint test
testEndpointAccessibility().then(() => {
  console.log('\n================================');
  console.log('R2 API Configuration Test Complete');
  console.log('================================');
  
  console.log('\nPossible Issues:');
  console.log('1. SSL/TLS compatibility between Node.js and Cloudflare R2');
  console.log('2. Firewall or network restrictions');
  console.log('3. R2 credentials might be invalid or expired');
  console.log('4. R2 bucket might not exist or be inaccessible');
  console.log('5. Account might not have proper R2 permissions');
  
  console.log('\nRecommended Actions:');
  console.log('1. Verify credentials in Cloudflare dashboard');
  console.log('2. Check if bucket exists and is accessible');
  console.log('3. Try using presigned URLs instead of direct uploads');
  console.log('4. Consider using a different SSL/TLS configuration');
});
