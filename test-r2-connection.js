/**
 * Test script to verify R2 connection and SSL configuration
 */

const https = require('https');
const crypto = require('crypto');

// R2 configuration from environment
const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID || 'e6e8f16fca044c21183dbe6f5007951f';
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID || 'e8c1709286897de45a17e5de2984653f';
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY || 'b61b9bc1071a3fe5f28066fbcdba47c2bf18fb281a54d4810046b7b666c108db';
const R2_BUCKET_NAME = process.env.R2_BUCKET_NAME || 'thetribelab';

function createSignature(method, path, queryString = '', headers = {}, body = '') {
  const now = new Date();
  const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
  const dateStamp = amzDate.substring(0, 8);
  
  const region = 'auto';
  const service = 's3';
  
  // Create canonical headers
  const canonicalHeaders = Object.keys(headers)
    .sort()
    .map(key => `${key.toLowerCase()}:${headers[key]}`)
    .join('\n') + '\n';
  
  const signedHeaders = Object.keys(headers)
    .sort()
    .map(key => key.toLowerCase())
    .join(';');
  
  // Create canonical request
  const canonicalRequest = [
    method,
    path,
    queryString,
    canonicalHeaders,
    signedHeaders,
    sha256(body)
  ].join('\n');
  
  // Create string to sign
  const algorithm = 'AWS4-HMAC-SHA256';
  const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
  const stringToSign = [
    algorithm,
    amzDate,
    credentialScope,
    sha256(canonicalRequest)
  ].join('\n');
  
  // Calculate signature
  const signingKey = getSignatureKey(dateStamp, region, service);
  const signature = crypto.createHmac('sha256', signingKey)
    .update(stringToSign)
    .digest('hex');
  
  // Create authorization header
  const authorization = `${algorithm} Credential=${R2_ACCESS_KEY_ID}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
  
  return {
    ...headers,
    'Authorization': authorization,
    'X-Amz-Date': amzDate,
  };
}

function sha256(data) {
  return crypto.createHash('sha256').update(data).digest('hex');
}

function getSignatureKey(dateStamp, region, service) {
  const kDate = crypto.createHmac('sha256', 'AWS4' + R2_SECRET_ACCESS_KEY).update(dateStamp).digest();
  const kRegion = crypto.createHmac('sha256', kDate).update(region).digest();
  const kService = crypto.createHmac('sha256', kRegion).update(service).digest();
  const kSigning = crypto.createHmac('sha256', kService).update('aws4_request').digest();
  return kSigning;
}

async function testR2Connection() {
  console.log('Testing R2 connection...');
  console.log(`Account ID: ${R2_ACCOUNT_ID}`);
  console.log(`Bucket: ${R2_BUCKET_NAME}`);
  
  const endpoint = `${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`;
  const path = `/${R2_BUCKET_NAME}/`;
  
  const headers = {
    'Host': endpoint,
  };

  const signedHeaders = createSignature('GET', path, '', headers);
  
  // Test with different SSL configurations
  const agents = [
    {
      name: 'Default Agent',
      agent: new https.Agent({
        keepAlive: true,
        timeout: 30000,
      })
    },
    {
      name: 'TLS 1.2 Agent',
      agent: new https.Agent({
        keepAlive: true,
        timeout: 30000,
        secureProtocol: 'TLSv1_2_method',
      })
    },
    {
      name: 'Compatible Ciphers Agent',
      agent: new https.Agent({
        keepAlive: true,
        timeout: 30000,
        secureProtocol: 'TLSv1_2_method',
        ciphers: [
          'ECDHE-RSA-AES128-GCM-SHA256',
          'ECDHE-RSA-AES256-GCM-SHA384',
          'ECDHE-RSA-AES128-SHA256',
          'ECDHE-RSA-AES256-SHA384'
        ].join(':'),
      })
    }
  ];

  for (const { name, agent } of agents) {
    console.log(`\nTesting with ${name}...`);
    
    try {
      const response = await new Promise((resolve, reject) => {
        const req = https.request({
          hostname: endpoint,
          path: path,
          method: 'GET',
          headers: signedHeaders,
          agent: agent,
          timeout: 30000,
        }, (res) => {
          console.log(`Status: ${res.statusCode}`);
          console.log(`Headers:`, res.headers);
          
          let data = '';
          res.on('data', (chunk) => {
            data += chunk;
          });
          
          res.on('end', () => {
            resolve({ statusCode: res.statusCode, data, headers: res.headers });
          });
        });
        
        req.on('error', (error) => {
          reject(error);
        });
        
        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout'));
        });
        
        req.end();
      });
      
      console.log(`✅ ${name} - Success! Status: ${response.statusCode}`);
      if (response.statusCode === 200) {
        console.log('🎉 R2 connection working!');
        break;
      }
      
    } catch (error) {
      console.log(`❌ ${name} - Failed:`, error.message);
      if (error.code) {
        console.log(`   Error code: ${error.code}`);
      }
    }
  }
}

// Run the test
testR2Connection().catch(console.error);
