"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";

export default function PaymentReturnDetector() {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Skip if already on payment-related pages
    if (pathname.includes('/payment-processing') || 
        pathname.includes('/payment-return') ||
        pathname.includes('/admin/communities')) {
      return;
    }

    // Check if there's a pending subscription
    const pendingSubscriptionData = localStorage.getItem('pendingSubscription');
    
    if (pendingSubscriptionData) {
      try {
        const pendingSubscription = JSON.parse(pendingSubscriptionData);
        
        // Check if the subscription is not too old (max 1 hour)
        const maxAge = 60 * 60 * 1000; // 1 hour
        if (Date.now() - pendingSubscription.timestamp < maxAge) {
          // User has returned with a pending subscription, redirect to payment-return for better handling
          console.log('Detected return from payment, redirecting to payment return page');
          router.push('/payment-return');
        } else {
          // Subscription is too old, clean it up
          localStorage.removeItem('pendingSubscription');
        }
      } catch (error) {
        console.error('Error parsing pending subscription data:', error);
        localStorage.removeItem('pendingSubscription');
      }
    }
  }, [pathname, router]);

  // This component doesn't render anything
  return null;
}
