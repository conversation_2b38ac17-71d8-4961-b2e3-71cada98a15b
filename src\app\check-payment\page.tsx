"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

export default function CheckPaymentPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [checking, setChecking] = useState(false);
  const [message, setMessage] = useState('');

  const checkPendingPayment = async () => {
    setChecking(true);
    setMessage('Checking for pending payments...');

    try {
      // Check if there's a pending subscription in localStorage
      const pendingSubscriptionData = localStorage.getItem('pendingSubscription');
      
      if (pendingSubscriptionData) {
        const pendingSubscription = JSON.parse(pendingSubscriptionData);
        
        // Check if it's not too old (max 2 hours)
        const maxAge = 2 * 60 * 60 * 1000; // 2 hours
        if (Date.now() - pendingSubscription.timestamp < maxAge) {
          setMessage('Found pending payment. Redirecting to verification...');
          setTimeout(() => {
            router.push('/payment-processing');
          }, 1500);
          return;
        } else {
          // Clean up old pending subscription
          localStorage.removeItem('pendingSubscription');
        }
      }

      // Check recent subscriptions via API
      const response = await fetch('/api/community-subscriptions/recent');
      const data = await response.json();

      if (response.ok && data.subscriptions?.length > 0) {
        const recentSubscription = data.subscriptions[0];
        if (recentSubscription.status === 'active') {
          setMessage('Found active subscription! Redirecting to your community...');
          setTimeout(() => {
            if (recentSubscription.communitySlug) {
              router.push(`/Newcompage/${recentSubscription.communitySlug}?subscription=verified`);
            } else {
              router.push('/admin/communities?subscription=verified');
            }
          }, 1500);
        } else {
          setMessage(`Found subscription with status: ${recentSubscription.status}. Please contact support if you completed payment.`);
        }
      } else {
        setMessage('No recent payments found. If you completed a payment, please contact support.');
      }
    } catch (error) {
      console.error('Error checking payment:', error);
      setMessage('Error checking payment status. Please try again or contact support.');
    } finally {
      setChecking(false);
    }
  };

  useEffect(() => {
    if (session) {
      checkPendingPayment();
    }
  }, [session]);

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-gray-600">Please log in to check your payment status.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Check Payment Status</h1>
        
        {checking && (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        )}
        
        <p className="text-gray-600 mb-6">{message}</p>
        
        {!checking && (
          <div className="space-y-4">
            <button
              onClick={checkPendingPayment}
              className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Check Again
            </button>
            
            <button
              onClick={() => router.push('/admin/communities')}
              className="w-full bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        )}
        
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Need Help?</h3>
          <p className="text-sm text-blue-800">
            If you completed a payment but don't see your subscription activated, 
            please contact our support team with your payment details.
          </p>
        </div>
      </div>
    </div>
  );
}
